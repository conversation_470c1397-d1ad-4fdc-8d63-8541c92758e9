import Fastify from 'fastify'
import cors from '@fastify/cors'
import jwt from '@fastify/jwt'
import { config } from 'dotenv'
import { createClient } from '@supabase/supabase-js'
import batchRoutes from './routes/batch'

// Load environment variables
config()

const fastify = Fastify({
  logger: true
})

// Register CORS
fastify.register(cors, {
  origin: ['http://localhost:3000', 'http://127.0.0.1:3000'],
  credentials: true
})

// Register JWT
fastify.register(jwt, {
  secret: process.env.JWT_SECRET || 'demo-jwt-secret-key-for-development'
})

// Initialize Supabase client (optional for demo)
let supabase: any = null
try {
  const supabaseUrl = process.env.SUPABASE_URL
  const supabaseKey = process.env.SUPABASE_ANON_KEY || process.env.SUPABASE_SERVICE_ROLE_KEY
  
  if (supabaseUrl && supabaseKey && supabaseUrl !== 'your-supabase-url' && supabaseKey !== 'your-supabase-anon-key') {
    supabase = createClient(supabaseUrl, supabaseKey)
    console.log('✅ Supabase client initialized')
  } else {
    console.log('⚠️  Supabase not configured - using mock data')
  }
} catch (error) {
  console.log('⚠️  Supabase initialization failed - using mock data')
}

// Health check route
fastify.get('/health', async (request, reply) => {
  return { 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    supabase: supabase ? 'connected' : 'mock'
  }
})

// API routes
fastify.get('/api/claims', async (request, reply) => {
  try {
    if (supabase) {
      const { data, error } = await supabase
        .from('claims')
        .select('*')
        .limit(10)

      if (error) {
        reply.code(500).send({ error: error.message })
        return
      }

      return { data }
    } else {
      // Return mock data when Supabase is not available
      return {
        data: [
          {
            id: '1',
            claim_number: 'CLM-001',
            claimant_name: 'John Doe',
            state: 'CA',
            amount_reported: 15000,
            status: 'new',
            created_at: '2024-01-15T00:00:00Z'
          },
          {
            id: '2',
            claim_number: 'CLM-002',
            claimant_name: 'Jane Smith',
            state: 'TX',
            amount_reported: 25000,
            status: 'contacted',
            created_at: '2024-01-14T00:00:00Z'
          }
        ]
      }
    }
  } catch (error) {
    reply.code(500).send({ error: 'Internal server error' })
  }
})

fastify.get('/api/dashboard/stats', async (request, reply) => {
  try {
    // Mock data for now - will be replaced with real Supabase queries when configured
    const stats = {
      totalClaims: 1234,
      activeClaims: 456,
      totalValue: 2400000,
      successRate: 18.2,
      teamPerformance: 92
    }

    return { data: stats }
  } catch (error) {
    reply.code(500).send({ error: 'Internal server error' })
  }
})

// Register batch import routes
fastify.register(batchRoutes, { prefix: '/api/batch' })

// Start server
const start = async () => {
  try {
    const port = parseInt(process.env.PORT || '3001')
    await fastify.listen({ port, host: '0.0.0.0' })
    console.log(`🚀 Server running on http://localhost:${port}`)
    console.log(`📊 Batch Import API: http://localhost:${port}/api/batch`)
    console.log(`🏥 Health Check: http://localhost:${port}/health`)
  } catch (err) {
    fastify.log.error(err)
    process.exit(1)
  }
}

start() 