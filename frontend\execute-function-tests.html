<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AssetHunterPro Function Tests</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
        }
        .pass { background: rgba(34, 197, 94, 0.2); border-left: 4px solid #22c55e; }
        .fail { background: rgba(239, 68, 68, 0.2); border-left: 4px solid #ef4444; }
        .warning { background: rgba(245, 158, 11, 0.2); border-left: 4px solid #f59e0b; }
        .summary {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
            text-align: center;
        }
        .button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: transform 0.2s;
        }
        .button:hover {
            transform: translateY(-2px);
        }
        .progress {
            width: 100%;
            height: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }
        .progress-bar {
            height: 100%;
            background: linear-gradient(45deg, #22c55e, #16a34a);
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 AssetHunterPro Function Tests</h1>
            <p>Comprehensive testing of all application functions</p>
            <button class="button" onclick="runAllTests()">🚀 Run All Tests</button>
            <button class="button" onclick="runQuickTests()">⚡ Quick Test</button>
            <button class="button" onclick="clearResults()">🗑️ Clear Results</button>
        </div>

        <div class="progress">
            <div class="progress-bar" id="progressBar"></div>
        </div>

        <div id="testResults"></div>

        <div class="summary" id="summary" style="display: none;">
            <h2>📊 Test Summary</h2>
            <div id="summaryContent"></div>
        </div>
    </div>

    <script>
        let testResults = {
            total: 0,
            passed: 0,
            failed: 0,
            warnings: 0,
            tests: []
        };

        function updateProgress(current, total) {
            const percentage = (current / total) * 100;
            document.getElementById('progressBar').style.width = percentage + '%';
        }

        function logTest(name, status, details = {}) {
            testResults.total++;
            testResults[status === 'pass' ? 'passed' : status === 'fail' ? 'failed' : 'warnings']++;
            
            const test = { name, status, details, timestamp: new Date().toISOString() };
            testResults.tests.push(test);

            const resultsDiv = document.getElementById('testResults');
            const testDiv = document.createElement('div');
            testDiv.className = `test-result ${status}`;
            
            const icon = status === 'pass' ? '✅' : status === 'fail' ? '❌' : '⚠️';
            testDiv.innerHTML = `
                <strong>${icon} ${name}</strong>
                ${details.issue ? `<br>Issue: ${details.issue}` : ''}
                ${details.fix ? `<br>Fix: ${details.fix}` : ''}
            `;
            
            resultsDiv.appendChild(testDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function createTestSection(title) {
            const resultsDiv = document.getElementById('testResults');
            const sectionDiv = document.createElement('div');
            sectionDiv.className = 'test-section';
            sectionDiv.innerHTML = `<h3>${title}</h3>`;
            resultsDiv.appendChild(sectionDiv);
            return sectionDiv;
        }

        async function runQuickTests() {
            clearResults();
            console.log('🚀 Running Quick Tests...');
            
            createTestSection('🔍 Quick Environment Tests');
            
            // Test 1: Browser basics
            logTest('localStorage', 
                (() => {
                    try {
                        localStorage.setItem('test', 'ok');
                        const result = localStorage.getItem('test') === 'ok';
                        localStorage.removeItem('test');
                        return result ? 'pass' : 'fail';
                    } catch (e) { return 'fail'; }
                })(),
                { issue: 'localStorage not working', fix: 'Enable localStorage in browser' }
            );

            logTest('fetch API', 
                typeof fetch === 'function' ? 'pass' : 'fail',
                { issue: 'fetch API not available', fix: 'Use modern browser' }
            );

            logTest('Development server port',
                window.location.port === '3005' ? 'pass' : 'fail',
                { issue: `Wrong port: ${window.location.port}`, fix: 'Start server on port 3005' }
            );

            // Test 2: Backend API
            createTestSection('🌐 Backend API Tests');
            try {
                const response = await fetch('http://localhost:3001/health');
                logTest('Backend health endpoint',
                    response.ok ? 'pass' : 'fail',
                    { issue: `Health endpoint returned ${response.status}`, fix: 'Start backend: cd backend && npm run dev' }
                );
            } catch (error) {
                logTest('Backend health endpoint', 'fail',
                    { issue: `Cannot reach backend: ${error.message}`, fix: 'Start backend: cd backend && npm run dev' }
                );
            }

            updateProgress(testResults.total, testResults.total);
            showSummary();
        }

        async function runAllTests() {
            clearResults();
            console.log('🧪 Running Comprehensive Tests...');
            
            const totalEstimatedTests = 25;
            let currentTest = 0;

            // Phase 1: Environment
            createTestSection('🔍 Phase 1: Environment Testing');
            
            const envTests = [
                { name: 'localStorage', test: () => {
                    try {
                        localStorage.setItem('test', 'ok');
                        const result = localStorage.getItem('test') === 'ok';
                        localStorage.removeItem('test');
                        return result;
                    } catch (e) { return false; }
                }},
                { name: 'sessionStorage', test: () => {
                    try {
                        sessionStorage.setItem('test', 'ok');
                        const result = sessionStorage.getItem('test') === 'ok';
                        sessionStorage.removeItem('test');
                        return result;
                    } catch (e) { return false; }
                }},
                { name: 'fetch API', test: () => typeof fetch === 'function' },
                { name: 'Promise support', test: () => typeof Promise === 'function' },
                { name: 'JSON support', test: () => typeof JSON === 'object' && typeof JSON.parse === 'function' }
            ];

            envTests.forEach(test => {
                currentTest++;
                updateProgress(currentTest, totalEstimatedTests);
                
                try {
                    const result = test.test();
                    logTest(`Environment: ${test.name}`, result ? 'pass' : 'fail',
                        { issue: result ? null : `${test.name} not working`, fix: `Enable ${test.name} support` }
                    );
                } catch (error) {
                    logTest(`Environment: ${test.name}`, 'fail',
                        { issue: `${test.name} threw error: ${error.message}`, fix: `Debug ${test.name}` }
                    );
                }
            });

            // Phase 2: Server Tests
            createTestSection('🌐 Phase 2: Server Testing');
            
            const serverTests = [
                { name: 'Development server port', test: () => window.location.port === '3005' },
                { name: 'Development server protocol', test: () => window.location.protocol === 'http:' },
                { name: 'Development server host', test: () => window.location.hostname === 'localhost' }
            ];

            serverTests.forEach(test => {
                currentTest++;
                updateProgress(currentTest, totalEstimatedTests);
                
                const result = test.test();
                logTest(`Server: ${test.name}`, result ? 'pass' : 'warning',
                    { issue: result ? null : `${test.name} check failed`, fix: `Configure ${test.name} properly` }
                );
            });

            // Phase 3: Backend API Tests
            createTestSection('🔌 Phase 3: Backend API Testing');
            
            const apiEndpoints = [
                { name: 'Health endpoint', url: 'http://localhost:3001/health' },
                { name: 'Claims API', url: 'http://localhost:3001/api/claims' },
                { name: 'Dashboard API', url: 'http://localhost:3001/api/dashboard/stats' }
            ];

            for (const endpoint of apiEndpoints) {
                currentTest++;
                updateProgress(currentTest, totalEstimatedTests);
                
                try {
                    const response = await fetch(endpoint.url);
                    logTest(`API: ${endpoint.name}`, response.ok ? 'pass' : 'fail',
                        { issue: response.ok ? null : `${endpoint.name} returned ${response.status}`, 
                          fix: response.ok ? null : 'Check backend server and endpoint implementation' }
                    );
                } catch (error) {
                    logTest(`API: ${endpoint.name}`, 'fail',
                        { issue: `Cannot reach ${endpoint.name}: ${error.message}`, 
                          fix: 'Start backend server: cd backend && npm run dev' }
                    );
                }
            }

            // Phase 4: UI Tests
            createTestSection('🎨 Phase 4: UI Component Testing');
            
            const uiTests = [
                { name: 'React app loaded', selector: '[data-reactroot], #root' },
                { name: 'CSS styles loaded', test: () => document.querySelectorAll('link[rel="stylesheet"], style').length > 0 },
                { name: 'Interactive buttons', selector: 'button' },
                { name: 'Form inputs', selector: 'input' },
                { name: 'Navigation elements', selector: 'nav, [role="navigation"]' }
            ];

            uiTests.forEach(test => {
                currentTest++;
                updateProgress(currentTest, totalEstimatedTests);
                
                let result;
                if (test.test) {
                    result = test.test();
                } else {
                    result = document.querySelector(test.selector) !== null;
                }
                
                logTest(`UI: ${test.name}`, result ? 'pass' : 'warning',
                    { issue: result ? null : `${test.name} not found`, fix: `Ensure ${test.name} is properly rendered` }
                );
            });

            // Phase 5: Data Validation Tests
            createTestSection('✅ Phase 5: Data Validation Testing');
            
            const validationTests = [
                { name: 'Email validation', test: () => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test('<EMAIL>') },
                { name: 'Phone validation', test: () => /^\+?[\d\s\-\(\)]{10,}$/.test('************') },
                { name: 'SSN validation', test: () => /^\d{3}-?\d{2}-?\d{4}$/.test('***********') }
            ];

            validationTests.forEach(test => {
                currentTest++;
                updateProgress(currentTest, totalEstimatedTests);
                
                try {
                    const result = test.test();
                    logTest(`Validation: ${test.name}`, result ? 'pass' : 'fail',
                        { issue: result ? null : `${test.name} regex not working`, fix: `Fix ${test.name} validation logic` }
                    );
                } catch (error) {
                    logTest(`Validation: ${test.name}`, 'fail',
                        { issue: `${test.name} threw error: ${error.message}`, fix: `Debug ${test.name} validation` }
                    );
                }
            });

            updateProgress(totalEstimatedTests, totalEstimatedTests);
            showSummary();
        }

        function showSummary() {
            const summaryDiv = document.getElementById('summary');
            const summaryContent = document.getElementById('summaryContent');
            
            const successRate = ((testResults.passed / testResults.total) * 100).toFixed(1);
            const status = successRate > 80 ? '🟢 HEALTHY' : successRate > 60 ? '🟡 NEEDS ATTENTION' : '🔴 CRITICAL';
            
            summaryContent.innerHTML = `
                <h3>Overall System Health: ${status}</h3>
                <p><strong>Success Rate:</strong> ${successRate}%</p>
                <p><strong>Total Tests:</strong> ${testResults.total}</p>
                <p><strong>✅ Passed:</strong> ${testResults.passed}</p>
                <p><strong>⚠️ Warnings:</strong> ${testResults.warnings}</p>
                <p><strong>❌ Failed:</strong> ${testResults.failed}</p>
                <p><strong>Timestamp:</strong> ${new Date().toLocaleString()}</p>
            `;
            
            summaryDiv.style.display = 'block';
            
            // Save results to localStorage
            localStorage.setItem('assetHunterPro_testResults', JSON.stringify({
                timestamp: new Date().toISOString(),
                summary: { successRate: parseFloat(successRate), status },
                results: testResults
            }));
            
            console.log('📊 Test Results:', testResults);
        }

        function clearResults() {
            testResults = { total: 0, passed: 0, failed: 0, warnings: 0, tests: [] };
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('summary').style.display = 'none';
            document.getElementById('progressBar').style.width = '0%';
        }

        // Auto-run quick tests on load
        window.addEventListener('load', () => {
            setTimeout(() => {
                console.log('🚀 AssetHunterPro Function Tests Ready!');
                console.log('Click "Run All Tests" for comprehensive testing or "Quick Test" for immediate results.');
            }, 1000);
        });
    </script>
</body>
</html>
